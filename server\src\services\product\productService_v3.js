const NewProduct = require('../../models/NewProduct');
const axios = require('axios');

/**
 * 产品服务 V3 - 基于 NewProduct 数据结构和 AI 智能对比
 * 支持全新的产品对比功能，使用 AI 生成结构化的 JSON 格式对比报告
 */

// DeepSeek API配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '***********************************';
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// 默认配置
const DEFAULT_CONFIG = {
  temperature: 0.4,
  maxTokens: 8000, // 增加token限制以支持更详细的分析
  timeout: 180000  // 增加超时时间
};

/**
 * 调用DeepSeek API
 * @param {String} userPrompt 用户提示
 * @param {String} systemPrompt 系统提示
 * @param {Object} config 配置选项
 * @returns {Promise<String>} API响应内容
 */
async function callDeepSeekAPI(userPrompt, systemPrompt = null, config = {}) {
  try {
    const aiConfig = { ...DEFAULT_CONFIG, ...config };
    
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    console.log(`🤖 正在调用DeepSeek AI进行智能产品分析...`);
    
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens,
        stream: false
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      console.log(`✅ DeepSeek AI分析完成`);
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ DeepSeek API调用失败:', error.message);
    throw new Error(`DeepSeek AI分析失败: ${error.message}`);
  }
}

/**
 * 基于数据库产品数据生成结构化的智能对比报告
 * @param {Array} formattedProducts 格式化后的产品数据
 * @returns {Promise<Object>} 结构化的对比分析报告
 */
async function generateStructuredComparisonReport(formattedProducts) {
  const systemPrompt = `你是一个资深的产品评测专家，擅长进行深入的产品对比分析。

请根据提供的产品信息，生成一份专业、全面的产品对比报告，并以严格的JSON格式返回。

分析要求：
1. **全面性要求**：必须覆盖产品数据中的所有重要技术参数，不能遗漏任何关键信息
2. **技术规格分析**：仔细分析产品数据中的每一个技术参数
3. **分类原则**：根据产品实际参数数据，按照逻辑相关性进行合理分类，确保每个重要参数都被包含
4. **分析深度**：analysis字段要详细且专业，包含各产品的具体参数值、参数差异说明、对实际使用体验的影响分析
5. **实用性**：优缺点分析要具体且实用，基于实际的技术参数差异
6. **场景化**：使用场景推荐要具体到实际应用，基于产品的实际特性
7. **购买指导**：购买建议要包含价格分析、购买时机、注意事项等实用信息
8. **客观性**：客观公正，严格基于提供的产品数据进行分析
9. **可读性**：语言通俗易懂，但要专业准确

JSON格式要求：
{
  "summary": {
    "title": "对比报告标题",
    "productCount": 产品数量,
    "category": "产品类别",
    "keyDifferences": ["基于实际参数差异的关键差异点1", "关键差异点2", "关键差异点3", "关键差异点4", "关键差异点5"]
  },
  "technicalSpecs": [
    {
      "category": "根据实际产品参数确定的技术类别名称（如：外观设计、屏幕显示、处理器性能、摄像头系统等）",
      "items": [
        {
          "name": "具体的技术参数名称（如：尺寸重量、材质工艺等）",
          "analysis": "详细分析该参数对实际使用体验的影响，要基于具体的技术数据。包含各产品的具体参数值、参数差异说明、对实际使用体验的影响分析、哪些参数更优以及为什么。格式参考：产品A：具体参数值，产品B：具体参数值。分析：详细解释参数差异对实际使用体验的影响，说明哪些参数更优以及原因。"
        }
      ]
    }
  ],
  "prosAndCons": [
    {
      "productName": "产品名称",
      "pros": ["基于实际参数的具体优点1", "具体优点2", "具体优点3", "具体优点4", "具体优点5", "具体优点6"],
      "cons": ["基于实际参数的具体缺点1", "具体缺点2", "具体缺点3", "具体缺点4"],
      "overallRating": "详细的综合评价，要基于实际技术参数说明适合什么用户群体"
    }
  ],
  "usageScenarios": [
    {
      "scenario": "基于产品实际特性的具体使用场景名称",
      "description": "详细的场景描述，说明具体的使用需求",
      "recommendedProduct": "推荐产品名称",
      "reason": "详细的推荐理由，要基于产品的实际技术特性说明为什么适合这个场景"
    }
  ],
  "purchaseAdvice": {
    "budgetConsiderations": "基于实际价格数据的详细预算分析，包含不同配置的价格对比和性价比分析",
    "generalRecommendation": "详细的总体购买建议，要基于产品的实际特性和价格",
    "specificAdvice": [
      {
        "userType": "具体用户类型",
        "recommendation": "详细的针对性建议,要基于产品的实际特性"
      }
    ],
    "purchaseTiming": "基于产品发布时间和市场情况的购买时机建议",
    "importantNotes": ["基于产品实际特性的重要注意事项1", "重要注意事项2", "重要注意事项3"]
  }
}`;

  const userPrompt = `请基于以下从数据库获取的产品信息生成结构化的对比分析报告：

产品数据：
${JSON.stringify(formattedProducts, null, 2)}

重要提醒：
1. 请仔细检查产品数据中的每一个字段和参数，确保在technicalSpecs中全面覆盖
2. 对于每个产品数据中存在的参数，都应该在对应的技术类别中进行分析
3. 如果某个参数在不同产品中的表达方式不同，请统一进行对比分析
4. 确保生成的JSON格式严格符合要求，特别是technicalSpecs部分要全面且详细`;

  const response = await callDeepSeekAPI(userPrompt, systemPrompt);
  
  try {
    // 尝试解析JSON响应
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('AI响应中未找到有效的JSON格式');
    }
    
    const parsedReport = JSON.parse(jsonMatch[0]);
    return parsedReport;
  } catch (parseError) {
    console.error('❌ JSON解析失败:', parseError.message);
    console.log('原始AI响应:', response);
    
    // 如果JSON解析失败，返回一个包含原始文本的备用格式
    return {
      summary: {
        title: "产品对比报告",
        productCount: formattedProducts.length,
        category: "未知类别",
        keyDifferences: ["AI分析格式解析失败"]
      },
      technicalSpecs: [],
      prosAndCons: [],
      usageScenarios: [],
      purchaseAdvice: {
        budgetConsiderations: "请查看原始分析文本",
        generalRecommendation: "AI分析格式解析失败，请联系技术支持",
        specificAdvice: []
      },
      rawAnalysis: response, // 保留原始分析文本作为备用
      parseError: parseError.message
    };
  }
}

/**
 * 根据产品名称列表获取产品参数对比数据（V3版本 - AI智能对比，JSON格式输出）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 对比结果
 */
const compareProductsByNamesV3 = async (productNames) => {
  try {
    console.log('🔍 开始产品对比 V3 - AI智能分析（JSON格式）');
    console.log('待对比产品:', productNames);

    // 1. 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2) {
      return {
        success: false,
        error: '至少需要提供2个产品名称进行对比',
        data: null
      };
    }

    if (productNames.length > 6) {
      return {
        success: false,
        error: '最多支持6个产品同时对比',
        data: null
      };
    }

    // 2. 从 NewProduct 数据库中查找产品
    const findResult = await findProductsByNamesV3(productNames);

    if (!findResult.success) {
      return findResult;
    }

    const { products, notFoundProducts } = findResult.data;

    console.log(`✅ 找到 ${products.length} 个产品用于对比`);
    if (notFoundProducts.length > 0) {
      console.log(`⚠️ 未找到的产品: ${notFoundProducts.join(', ')}`);
    }

    // 3. 格式化产品数据用于 AI 分析
    const formattedProductData = products.map(product => formatProductForAI(product));

    // 4. 使用 AI 进行产品对比分析（生成结构化JSON报告）
    console.log('🤖 调用 AI 进行智能产品对比分析（JSON格式）...');
    const structuredReport = await generateStructuredComparisonReport(formattedProductData);

    if (!structuredReport) {
      return {
        success: false,
        error: 'AI 分析失败：未能生成对比报告',
        data: null
      };
    }

    // 5. 分析产品类别信息
    const productTypes = [...new Set(products.map(p => p.productType))];
    const productCategory = productTypes.length === 1 ? productTypes[0] : '混合类别';
    const isSameCategory = productTypes.length === 1;
    const crossCategoryNote = !isSameCategory ?
      `检测到不同类型的产品: ${productTypes.join(', ')}，已进行跨类别对比分析` : null;

    // 6. 构建最终返回结果
    const result = {
      success: true,
      data: {
        // 基本信息
        requestedProducts: productNames,
        foundProducts: products.map(p => p.skuName),
        notFoundProducts: notFoundProducts,
        productCount: products.length,

        // 产品详细信息
        products: formattedProductData,

        // AI 结构化对比分析结果
        aiAnalysis: {
          productCategory: productCategory,
          isSameCategory: isSameCategory,
          crossCategoryNote: crossCategoryNote,
          structuredReport: structuredReport, // 结构化的JSON报告
          analysisTimestamp: new Date().toISOString(),
          dataSource: '数据库',
          aiModel: DEEPSEEK_MODEL,
          outputFormat: 'JSON'
        },

        // 版本信息
        version: 'v3',
        analysisMethod: 'AI智能对比（基于数据库数据，JSON格式输出）'
      }
    };

    console.log('✅ 产品对比 V3 完成');
    return result;

  } catch (error) {
    console.error('❌ 产品对比 V3 失败:', error);
    return {
      success: false,
      error: `产品对比失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 从 NewProduct 数据库中查找产品（V3版本）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 查找结果
 */
const findProductsByNamesV3 = async (productNames) => {
  try {
    const products = [];
    const notFoundProducts = [];

    for (const productName of productNames) {
      console.log(`🔍 搜索产品: ${productName}`);

      // 使用智能搜索匹配产品
      const product = await findSingleProductByName(productName);

      if (product) {
        products.push(product);
        console.log(`✅ 找到产品: ${product.skuName}`);
      } else {
        notFoundProducts.push(productName);
        console.log(`❌ 未找到产品: ${productName}`);
      }
    }

    if (products.length < 2) {
      return {
        success: false,
        error: `找到的产品数量不足，无法进行对比。找到 ${products.length} 个，需要至少 2 个产品`,
        data: null
      };
    }

    // 检查产品类型是否一致（可选警告，不强制要求）
    const productTypes = [...new Set(products.map(p => p.productType))];
    let categoryWarning = null;

    if (productTypes.length > 1) {
      categoryWarning = `检测到不同类型的产品: ${productTypes.join(', ')}，AI 将进行跨类别对比分析`;
      console.log(`⚠️ ${categoryWarning}`);
    }

    return {
      success: true,
      data: {
        products,
        notFoundProducts,
        categoryWarning
      }
    };

  } catch (error) {
    console.error('查找产品失败:', error);
    return {
      success: false,
      error: `查找产品失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 根据产品名称查找单个产品（精确匹配 skuName）
 * @param {String} productName 产品名称
 * @returns {Promise<Object|null>} 产品对象或null
 */
const findSingleProductByName = async (productName) => {
  try {
    // 直接通过 skuName 精确匹配
    const product = await NewProduct.findOne({
      skuName: productName.trim()
    });

    return product;

  } catch (error) {
    console.error(`查找单个产品失败 (${productName}):`, error);
    return null;
  }
};

/**
 * 格式化产品数据用于 AI 分析
 * @param {Object} product NewProduct 对象
 * @returns {Object} 格式化后的产品数据
 */
const formatProductForAI = (product) => {
  // 获取默认配置
  const defaultConfig = product.configurations?.find(
    config => config.name === product.defaultConfiguration
  ) || product.configurations?.[0];

  // 构建显示名称
  let displayName = product.skuName;
  if (defaultConfig && product.configurations?.length > 1) {
    // 如果有多个配置，在名称中包含默认配置信息
    const configInfo = [];
    if (defaultConfig.specs?.存储扩展?.ROM容量) {
      configInfo.push(defaultConfig.specs.存储扩展.ROM容量);
    }
    if (configInfo.length > 0) {
      displayName = `${product.skuName}(${configInfo.join('/')})`;
    }
  }

  // 确定显示价格
  let displayPrice = null;
  let priceRange = null;

  if (product.configurations && product.configurations.length > 0) {
    const prices = product.configurations
      .filter(config => config.price && config.price > 0)
      .map(config => config.price);

    if (prices.length > 0) {
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);

      if (minPrice === maxPrice) {
        displayPrice = minPrice;
      } else {
        priceRange = `¥${minPrice} - ¥${maxPrice}`;
        displayPrice = defaultConfig?.price > 0 ? defaultConfig.price : minPrice;
      }
    }
  }

  return {
    // 基本信息
    id: product._id,
    skuId: product.skuId,
    name: product.skuName,
    displayName: displayName,
    brand: product.brandName,
    productType: product.productType,
    category: product.category,
    image: product.imageUrl,

    // 价格信息
    price: displayPrice,
    priceRange: priceRange,

    // 配置信息
    configurations: product.configurations || [],
    defaultConfiguration: product.defaultConfiguration,
    defaultConfigDetails: defaultConfig ? {
      name: defaultConfig.name,
      price: defaultConfig.price,
      available: defaultConfig.available,
      specs: defaultConfig.specs
    } : null,

    // 规格参数
    commonSpecs: product.commonSpecs || {},

    // 支持对比
    supportsComparison: product.supportsComparison,

    // 数据转换信息
    conversionInfo: product.conversionInfo
  };
};



module.exports = {
  compareProductsByNamesV3,
  findProductsByNamesV3,
  formatProductForAI,
  generateStructuredComparisonReport
};
