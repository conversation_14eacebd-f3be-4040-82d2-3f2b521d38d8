const NewProduct = require('../../models/NewProduct');
const axios = require('axios');

/**
 * 产品服务 V4 - 基于参数字段提取的智能对比
 * 新增功能：
 * 1. 自动提取产品参数字段和类型
 * 2. 明确指导AI分析特定参数
 * 3. 确保参数分析的全面性和一致性
 */

// DeepSeek API配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '***********************************';
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// 默认配置
const DEFAULT_CONFIG = {
  temperature: 0.4,
  maxTokens: 10000, // 增加token限制以支持更详细的分析
  timeout: 180000  // 增加超时时间
};

/**
 * V4版本完全基于实际产品数据动态提取参数，不再依赖硬编码的参数映射
 * 这样可以确保：
 * 1. 适应数据结构的变化
 * 2. 自动发现新的参数字段
 * 3. 避免参数遗漏
 * 4. 支持任意产品类型
 */

/**
 * 调用DeepSeek API
 */
async function callDeepSeekAPI(userPrompt, systemPrompt = null, config = {}) {
  try {
    const aiConfig = { ...DEFAULT_CONFIG, ...config };
    
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    console.log(`🤖 正在调用DeepSeek AI进行智能产品分析...`);
    
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens,
        stream: false
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      console.log(`✅ DeepSeek AI分析完成`);
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ DeepSeek API调用失败:', error.message);
    throw new Error(`DeepSeek AI分析失败: ${error.message}`);
  }
}

/**
 * 提取产品参数字段和类型信息
 * @param {Array} formattedProducts 格式化后的产品数据
 * @returns {Object} 参数分析结果
 */
function extractProductParametersAndTypes(formattedProducts) {
  console.log('🔍 开始提取产品参数字段和类型信息...');
  
  // 1. 分析产品类型
  const productTypes = [...new Set(formattedProducts.map(p => p.productType))];
  const primaryProductType = productTypes[0]; // 使用第一个产品类型作为主要类型
  const isMixedTypes = productTypes.length > 1;
  
  console.log(`📋 检测到产品类型: ${productTypes.join(', ')}`);
  
  // 2. 收集所有参数字段
  const allParameters = new Set();
  const parametersByCategory = {};
  const parameterValues = {}; // 存储每个参数在不同产品中的值
  
  formattedProducts.forEach((product, index) => {
    console.log(`📊 分析产品 ${index + 1}: ${product.name}`);
    
    // 分析 commonSpecs
    if (product.commonSpecs) {
      Object.keys(product.commonSpecs).forEach(category => {
        if (!parametersByCategory[category]) {
          parametersByCategory[category] = new Set();
        }
        
        const categorySpecs = product.commonSpecs[category];
        if (typeof categorySpecs === 'object' && categorySpecs !== null) {
          Object.keys(categorySpecs).forEach(param => {
            allParameters.add(param);
            parametersByCategory[category].add(param);
            
            // 记录参数值
            if (!parameterValues[param]) {
              parameterValues[param] = [];
            }
            parameterValues[param].push({
              productName: product.name,
              value: categorySpecs[param],
              category: category
            });
          });
        }
      });
    }
    
    // 分析 configurations 中的 specs
    if (product.configurations) {
      product.configurations.forEach(config => {
        if (config.specs) {
          Object.keys(config.specs).forEach(category => {
            if (!parametersByCategory[category]) {
              parametersByCategory[category] = new Set();
            }
            
            const categorySpecs = config.specs[category];
            if (typeof categorySpecs === 'object' && categorySpecs !== null) {
              Object.keys(categorySpecs).forEach(param => {
                allParameters.add(param);
                parametersByCategory[category].add(param);
                
                // 记录参数值（配置级别）
                if (!parameterValues[param]) {
                  parameterValues[param] = [];
                }
                parameterValues[param].push({
                  productName: `${product.name} (${config.name})`,
                  value: categorySpecs[param],
                  category: category
                });
              });
            }
          });
        }
      });
    }
  });
  
  // 3. 转换 Set 为 Array 并排序
  const finalParametersByCategory = {};
  Object.keys(parametersByCategory).forEach(category => {
    finalParametersByCategory[category] = Array.from(parametersByCategory[category]).sort();
  });
  
  // 4. 分析参数分类的优先级（基于参数数量和重要性）
  const categoryPriority = Object.keys(finalParametersByCategory)
    .map(category => ({
      category: category,
      parameterCount: finalParametersByCategory[category].length
    }))
    .sort((a, b) => b.parameterCount - a.parameterCount);
  
  // 5. 分析参数覆盖情况
  const parameterCoverage = {};
  Array.from(allParameters).forEach(param => {
    const productCount = new Set(parameterValues[param].map(v => v.productName.split(' (')[0])).size;
    parameterCoverage[param] = {
      totalProducts: formattedProducts.length,
      coveredProducts: productCount,
      coverage: (productCount / formattedProducts.length * 100).toFixed(1) + '%'
    };
  });
  
  const result = {
    productTypes: productTypes,
    primaryProductType: primaryProductType,
    isMixedTypes: isMixedTypes,
    totalParameters: allParameters.size,
    parametersByCategory: finalParametersByCategory,
    parameterValues: parameterValues,
    parameterCoverage: parameterCoverage,
    categoryPriority: categoryPriority,
    extractedCategories: Object.keys(finalParametersByCategory).sort()
  };
  
  console.log(`✅ 参数提取完成: 发现 ${result.totalParameters} 个参数，分布在 ${result.extractedCategories.length} 个类别中`);

  return result;
}

/**
 * 基于参数字段提取生成结构化的智能对比报告 (V4版本)
 * @param {Array} formattedProducts 格式化后的产品数据
 * @param {Object} parameterAnalysis 参数分析结果
 * @returns {Promise<Object>} 结构化的对比分析报告
 */
async function generateStructuredComparisonReportV4(formattedProducts, parameterAnalysis) {
  const systemPrompt = `你是一个资深的产品评测专家，擅长进行深入的产品对比分析。

现在你需要根据提供的产品信息和明确的参数字段列表，生成一份专业、全面的产品对比报告，并以严格的JSON格式返回。

**重要：你必须严格按照提供的参数字段列表进行分析，确保每个指定的参数都被包含在技术规格分析中。**

分析要求：
1. **参数完整性**：必须分析提供的参数字段列表中的每一个参数，不能遗漏
2. **分类准确性**：严格按照提供的参数分类进行组织，保持分类的一致性
3. **数据准确性**：分析时要引用具体的参数值，确保数据准确
4. **对比深度**：对每个参数进行详细的对比分析，说明差异和影响
5. **实用性导向**：分析要关注对实际使用体验的影响
6. **客观公正**：基于实际数据进行客观分析，避免主观偏见

JSON格式要求：
{
  "summary": {
    "title": "对比报告标题",
    "productCount": 产品数量,
    "category": "产品类别",
    "analyzedParameters": 分析的参数总数,
    "parameterCategories": ["参数分类1", "参数分类2", ...],
    "keyDifferences": ["基于实际参数差异的关键差异点1", "关键差异点2", "关键差异点3", "关键差异点4", "关键差异点5"]
  },
  "technicalSpecs": [
    {
      "category": "参数分类名称（必须与提供的分类一致）",
      "parameters": ["该分类下的所有参数名称"],
      "items": [
        {
          "name": "具体的技术参数名称（必须与提供的参数列表一致）",
          "values": [
            {
              "productName": "产品名称",
              "value": "该产品的具体参数值",
              "note": "如有特殊说明"
            }
          ],
          "analysis": "详细分析该参数的对比情况，包括：1）各产品的具体参数值对比；2）参数差异对实际使用体验的影响；3）哪些参数表现更优以及原因；4）对用户选择的建议。"
        }
      ]
    }
  ],
  "prosAndCons": [
    {
      "productName": "产品名称",
      "pros": ["基于实际参数的具体优点1", "具体优点2", "具体优点3", "具体优点4", "具体优点5"],
      "cons": ["基于实际参数的具体缺点1", "具体缺点2", "具体缺点3", "具体缺点4"],
      "overallRating": "详细的综合评价，要基于实际技术参数说明适合什么用户群体"
    }
  ],
  "usageScenarios": [
    {
      "scenario": "基于产品实际特性的具体使用场景名称",
      "description": "详细的场景描述，说明具体的使用需求",
      "recommendedProduct": "推荐产品名称",
      "reason": "详细的推荐理由，要基于产品的实际技术特性说明为什么适合这个场景",
      "relevantParameters": ["与该场景相关的关键参数1", "关键参数2", "关键参数3"]
    }
  ],
  "purchaseAdvice": {
    "budgetConsiderations": "基于实际价格数据的详细预算分析，包含不同配置的价格对比和性价比分析",
    "generalRecommendation": "详细的总体购买建议，要基于产品的实际特性和价格",
    "specificAdvice": [
      {
        "userType": "具体用户类型",
        "recommendation": "详细的针对性建议,要基于产品的实际特性",
        "keyParameters": ["对该用户类型最重要的参数1", "重要参数2", "重要参数3"]
      }
    ],
    "purchaseTiming": "基于产品发布时间和市场情况的购买时机建议",
    "importantNotes": ["基于产品实际特性的重要注意事项1", "重要注意事项2", "重要注意事项3"]
  }
}`;

  // 构建详细的用户提示，包含参数字段信息
  const parameterInfo = Object.keys(parameterAnalysis.parametersByCategory).map(category => {
    const parameters = parameterAnalysis.parametersByCategory[category];
    return `**${category}**：${parameters.join('、')}`;
  }).join('\n');

  const userPrompt = `请基于以下产品信息和明确的参数字段列表生成结构化的对比分析报告：

## 产品信息：
${JSON.stringify(formattedProducts, null, 2)}

## 产品类型分析：
- 主要产品类型：${parameterAnalysis.primaryProductType}
- 所有产品类型：${parameterAnalysis.productTypes.join('、')}
- 是否混合类型：${parameterAnalysis.isMixedTypes ? '是' : '否'}

## 必须分析的参数字段列表（按分类）：
${parameterInfo}

## 参数覆盖情况：
总参数数量：${parameterAnalysis.totalParameters}
参数分类数量：${parameterAnalysis.extractedCategories.length}

**重要要求：**
1. 你必须在technicalSpecs中为每个上述参数分类创建对应的分析项
2. 每个分类下必须分析该分类中的所有参数
3. 如果某个参数在某些产品中不存在，请在分析中明确说明
4. 确保生成的JSON格式严格符合要求
5. 所有的分析都要基于实际的参数数据，不要编造信息`;

  const response = await callDeepSeekAPI(userPrompt, systemPrompt);

  try {
    // 尝试解析JSON响应
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('AI响应中未找到有效的JSON格式');
    }

    const parsedReport = JSON.parse(jsonMatch[0]);

    // 验证报告完整性
    const validationResult = validateReportCompleteness(parsedReport, parameterAnalysis);
    if (!validationResult.isValid) {
      console.warn('⚠️ 报告完整性验证失败:', validationResult.issues);
      parsedReport.validationWarnings = validationResult.issues;
    }

    return parsedReport;
  } catch (parseError) {
    console.error('❌ JSON解析失败:', parseError.message);
    console.log('原始AI响应:', response);

    // 如果JSON解析失败，返回一个包含原始文本的备用格式
    return {
      summary: {
        title: "产品对比报告",
        productCount: formattedProducts.length,
        category: parameterAnalysis.primaryProductType,
        analyzedParameters: parameterAnalysis.totalParameters,
        parameterCategories: parameterAnalysis.extractedCategories,
        keyDifferences: ["AI分析格式解析失败"]
      },
      technicalSpecs: [],
      prosAndCons: [],
      usageScenarios: [],
      purchaseAdvice: {
        budgetConsiderations: "请查看原始分析文本",
        generalRecommendation: "AI分析格式解析失败，请联系技术支持",
        specificAdvice: []
      },
      rawAnalysis: response, // 保留原始分析文本作为备用
      parseError: parseError.message,
      parameterAnalysis: parameterAnalysis // 包含参数分析结果
    };
  }
}

/**
 * 验证报告完整性
 * @param {Object} report 生成的报告
 * @param {Object} parameterAnalysis 参数分析结果
 * @returns {Object} 验证结果
 */
function validateReportCompleteness(report, parameterAnalysis) {
  const issues = [];

  // 检查是否包含所有参数分类
  const reportCategories = new Set(report.technicalSpecs?.map(spec => spec.category) || []);
  const expectedCategories = new Set(parameterAnalysis.extractedCategories);

  expectedCategories.forEach(category => {
    if (!reportCategories.has(category)) {
      issues.push(`缺少参数分类: ${category}`);
    }
  });

  // 检查每个分类下的参数是否完整
  report.technicalSpecs?.forEach(spec => {
    const expectedParams = parameterAnalysis.parametersByCategory[spec.category] || [];
    const reportParams = new Set(spec.items?.map(item => item.name) || []);

    expectedParams.forEach(param => {
      if (!reportParams.has(param)) {
        issues.push(`分类 "${spec.category}" 中缺少参数: ${param}`);
      }
    });
  });

  return {
    isValid: issues.length === 0,
    issues: issues
  };
}

/**
 * 根据产品名称列表获取产品参数对比数据（V4版本 - 基于参数字段提取的智能对比）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 对比结果
 */
const compareProductsByNamesV4 = async (productNames) => {
  try {
    console.log('🔍 开始产品对比 V4 - 基于参数字段提取的智能分析');
    console.log('待对比产品:', productNames);

    // 1. 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2) {
      return {
        success: false,
        error: '至少需要提供2个产品名称进行对比',
        data: null
      };
    }

    if (productNames.length > 6) {
      return {
        success: false,
        error: '最多支持6个产品同时对比',
        data: null
      };
    }

    // 2. 从 NewProduct 数据库中查找产品
    const findResult = await findProductsByNamesV4(productNames);

    if (!findResult.success) {
      return findResult;
    }

    const { products, notFoundProducts } = findResult.data;

    console.log(`✅ 找到 ${products.length} 个产品用于对比`);
    if (notFoundProducts.length > 0) {
      console.log(`⚠️ 未找到的产品: ${notFoundProducts.join(', ')}`);
    }

    // 3. 格式化产品数据用于 AI 分析
    const formattedProductData = products.map(product => formatProductForAI(product));

    // 4. 提取产品参数字段和类型信息
    console.log('🔍 提取产品参数字段和类型信息...');
    const parameterAnalysis = extractProductParametersAndTypes(formattedProductData);

    // 5. 使用 AI 进行产品对比分析（基于明确的参数字段）
    console.log('🤖 调用 AI 进行基于参数字段的智能产品对比分析...');
    const structuredReport = await generateStructuredComparisonReportV4(formattedProductData, parameterAnalysis);

    if (!structuredReport) {
      return {
        success: false,
        error: 'AI 分析失败：未能生成对比报告',
        data: null
      };
    }

    // 6. 分析产品类别信息
    const productTypes = [...new Set(products.map(p => p.productType))];
    const productCategory = productTypes.length === 1 ? productTypes[0] : '混合类别';
    const isSameCategory = productTypes.length === 1;
    const crossCategoryNote = !isSameCategory ?
      `检测到不同类型的产品: ${productTypes.join(', ')}，已进行跨类别对比分析` : null;

    // 7. 构建最终返回结果
    const result = {
      success: true,
      data: {
        // 基本信息
        requestedProducts: productNames,
        foundProducts: products.map(p => p.skuName),
        notFoundProducts: notFoundProducts,
        productCount: products.length,

        // 产品详细信息
        products: formattedProductData,

        // V4新增：参数分析信息
        parameterAnalysis: {
          productTypes: parameterAnalysis.productTypes,
          primaryProductType: parameterAnalysis.primaryProductType,
          isMixedTypes: parameterAnalysis.isMixedTypes,
          totalParameters: parameterAnalysis.totalParameters,
          parametersByCategory: parameterAnalysis.parametersByCategory,
          parameterCoverage: parameterAnalysis.parameterCoverage,
          extractedCategories: parameterAnalysis.extractedCategories
        },

        // AI 结构化对比分析结果
        aiAnalysis: {
          productCategory: productCategory,
          isSameCategory: isSameCategory,
          crossCategoryNote: crossCategoryNote,
          structuredReport: structuredReport, // 基于参数字段的结构化JSON报告
          analysisTimestamp: new Date().toISOString(),
          dataSource: '数据库',
          aiModel: DEEPSEEK_MODEL,
          outputFormat: 'JSON',
          analysisMethod: '基于参数字段提取的智能对比'
        },

        // 版本信息
        version: 'v4',
        analysisMethod: 'AI智能对比（基于参数字段提取，确保分析完整性）'
      }
    };

    console.log('✅ 产品对比 V4 完成');
    return result;

  } catch (error) {
    console.error('❌ 产品对比 V4 失败:', error);
    return {
      success: false,
      error: `产品对比失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 从 NewProduct 数据库中查找产品（V4版本）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 查找结果
 */
const findProductsByNamesV4 = async (productNames) => {
  try {
    const products = [];
    const notFoundProducts = [];

    for (const productName of productNames) {
      console.log(`🔍 搜索产品: ${productName}`);

      // 使用智能搜索匹配产品
      const product = await findSingleProductByName(productName);

      if (product) {
        products.push(product);
        console.log(`✅ 找到产品: ${product.skuName}`);
      } else {
        notFoundProducts.push(productName);
        console.log(`❌ 未找到产品: ${productName}`);
      }
    }

    if (products.length < 2) {
      return {
        success: false,
        error: `找到的产品数量不足，无法进行对比。找到 ${products.length} 个，需要至少 2 个产品`,
        data: null
      };
    }

    // 检查产品类型是否一致（可选警告，不强制要求）
    const productTypes = [...new Set(products.map(p => p.productType))];
    let categoryWarning = null;

    if (productTypes.length > 1) {
      categoryWarning = `检测到不同类型的产品: ${productTypes.join(', ')}，AI 将进行跨类别对比分析`;
      console.log(`⚠️ ${categoryWarning}`);
    }

    return {
      success: true,
      data: {
        products,
        notFoundProducts,
        categoryWarning
      }
    };

  } catch (error) {
    console.error('查找产品失败:', error);
    return {
      success: false,
      error: `查找产品失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 根据产品名称查找单个产品（精确匹配 skuName）
 * @param {String} productName 产品名称
 * @returns {Promise<Object|null>} 产品对象或null
 */
const findSingleProductByName = async (productName) => {
  try {
    // 直接通过 skuName 精确匹配
    const product = await NewProduct.findOne({
      skuName: productName.trim()
    });

    return product;

  } catch (error) {
    console.error(`查找单个产品失败 (${productName}):`, error);
    return null;
  }
};

/**
 * 格式化产品数据用于 AI 分析（V4版本，增强参数信息）
 * @param {Object} product NewProduct 对象
 * @returns {Object} 格式化后的产品数据
 */
const formatProductForAI = (product) => {
  // 获取默认配置
  const defaultConfig = product.configurations?.find(
    config => config.name === product.defaultConfiguration
  ) || product.configurations?.[0];

  // 构建显示名称
  let displayName = product.skuName;
  if (defaultConfig && product.configurations?.length > 1) {
    // 如果有多个配置，在名称中包含默认配置信息
    const configInfo = [];
    if (defaultConfig.specs?.存储扩展?.ROM容量) {
      configInfo.push(defaultConfig.specs.存储扩展.ROM容量);
    }
    if (configInfo.length > 0) {
      displayName = `${product.skuName}(${configInfo.join('/')})`;
    }
  }

  // 确定显示价格
  let displayPrice = null;
  let priceRange = null;

  if (product.configurations && product.configurations.length > 0) {
    const prices = product.configurations
      .filter(config => config.price && config.price > 0)
      .map(config => config.price);

    if (prices.length > 0) {
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);

      if (minPrice === maxPrice) {
        displayPrice = minPrice;
      } else {
        priceRange = `¥${minPrice} - ¥${maxPrice}`;
        displayPrice = defaultConfig?.price > 0 ? defaultConfig.price : minPrice;
      }
    }
  }

  return {
    // 基本信息
    id: product._id,
    skuId: product.skuId,
    name: product.skuName,
    displayName: displayName,
    brand: product.brandName,
    productType: product.productType,
    category: product.category,
    image: product.imageUrl,

    // 价格信息
    price: displayPrice,
    priceRange: priceRange,

    // 配置信息
    configurations: product.configurations || [],
    defaultConfiguration: product.defaultConfiguration,
    defaultConfigDetails: defaultConfig ? {
      name: defaultConfig.name,
      price: defaultConfig.price,
      available: defaultConfig.available,
      specs: defaultConfig.specs
    } : null,

    // 规格参数
    commonSpecs: product.commonSpecs || {},

    // 支持对比
    supportsComparison: product.supportsComparison,

    // 数据转换信息
    conversionInfo: product.conversionInfo
  };
};

/**
 * 分析参数分类的重要性得分（基于分类名称的语义）
 * @param {String} categoryName 分类名称
 * @returns {Number} 重要性得分
 */
const getCategoryImportanceScore = (categoryName) => {
  // 基于分类名称的关键词给出重要性得分
  const importanceKeywords = {
    '基本信息': 10,
    '外观设计': 8,
    '屏幕显示': 9,
    '处理器性能': 9,
    '存储扩展': 8,
    '摄像头': 7,
    '网络连接': 6,
    '电池充电': 8,
    '安全传感器': 6,
    '接口其他': 5
  };

  // 检查分类名称中是否包含重要关键词
  for (const [keyword, score] of Object.entries(importanceKeywords)) {
    if (categoryName.includes(keyword)) {
      return score;
    }
  }

  // 默认得分
  return 5;
};

/**
 * 生成参数分析摘要
 * @param {Object} parameterAnalysis 参数分析结果
 * @returns {Object} 参数分析摘要
 */
const generateParameterAnalysisSummary = (parameterAnalysis) => {
  const summary = {
    totalParameters: parameterAnalysis.totalParameters,
    totalCategories: parameterAnalysis.extractedCategories.length,
    productTypes: parameterAnalysis.productTypes,
    isMixedTypes: parameterAnalysis.isMixedTypes,
    categoriesWithMostParameters: [],
    parametersWithFullCoverage: [],
    parametersWithPartialCoverage: []
  };

  // 找出参数最多的分类
  const categoryCounts = Object.keys(parameterAnalysis.parametersByCategory).map(category => ({
    category: category,
    count: parameterAnalysis.parametersByCategory[category].length
  })).sort((a, b) => b.count - a.count);

  summary.categoriesWithMostParameters = categoryCounts.slice(0, 3);

  // 分析参数覆盖情况
  Object.keys(parameterAnalysis.parameterCoverage).forEach(param => {
    const coverage = parameterAnalysis.parameterCoverage[param];
    if (coverage.coveredProducts === coverage.totalProducts) {
      summary.parametersWithFullCoverage.push(param);
    } else {
      summary.parametersWithPartialCoverage.push({
        parameter: param,
        coverage: coverage.coverage
      });
    }
  });

  return summary;
};

module.exports = {
  compareProductsByNamesV4,
  findProductsByNamesV4,
  formatProductForAI,
  extractProductParametersAndTypes,
  generateStructuredComparisonReportV4,
  validateReportCompleteness,
  getStandardParameterCategories,
  generateParameterAnalysisSummary,
  PRODUCT_TYPE_PARAM_MAPPING
};
